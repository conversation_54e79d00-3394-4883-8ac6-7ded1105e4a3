<bci-page-content>
  <bci-master-view [isPadded]="false">
    <bci-commandbar [itemCount]="itemCount" [selectedItemCount]="0" [showSearch]="true" class="commandbar-padding"
      [showAdd]="canCreateCollectionAndPage" (clickAdd)="addCollection()" (search)="onSearchChange($event)">
    </bci-commandbar>

    <div class="content-wrapper">
      <h6>Collection: Pages collection that you want to chat with, the pages could come from different spaces.</h6>

      <div class="collections-content">
        <div *ngFor="let collection of collections" class="process-card">
          <mat-card bciCardColorState="{{ collection.isEmbedding ? 'green' : 'neutral' }}">
            <div class="card-row-grid">
              <bci-label-value-pair class="card-item name" label="Name"
                [value]="collection.name"></bci-label-value-pair>
              <bci-label-value-pair class="card-item embedding-model" label="Embedding Model"
                [value]="collection.embeddingModel"></bci-label-value-pair>
              <bci-label-value-pair class="card-item auto-update" label="Automatic Update"
                [value]="collection.isAutomaticUpdate ? 'Yes' : 'No'"></bci-label-value-pair>
              <bci-label-value-pair class="card-item update-type" label="Update Type"
                [value]="collection.updateType || 'N/A'"></bci-label-value-pair>
              <bci-label-value-pair class="card-item interval" label="Interval Number"
                [value]="collection.intervalNumber || 'N/A'"></bci-label-value-pair>
              <bci-label-value-pair class="card-item last-modified" label="Last Modified"
                [value]="(collection.modificationTime | date:'yyyy-MM-dd HH:mm:ss') || 'N/A'"></bci-label-value-pair>

              <div class="action-buttons">
                <button mat-icon-button class="action-btn add" title="Add Page"
                  [disabled]="!canCreateCollectionAndPage || !canCRUDKnowledge(collection)"
                  (click)="addPage(collection); $event.stopPropagation()">
                  <mat-icon fontIcon="bosch-ic-add"></mat-icon>
                </button>
                <button mat-icon-button class="action-btn update-embed" title="Update & Embed Collection Content"
                  (click)="updateAndEmbedCollectionContents(collection); $event.stopPropagation()">
                  <mat-icon fontIcon="bosch-ic-refresh-cloud"></mat-icon>
                </button>
                <button mat-icon-button class="action-btn edit" title="Edit Collection"
                  [disabled]="!canCRUDKnowledge(collection)"
                  (click)="editCollection(collection); $event.stopPropagation()">
                  <mat-icon fontIcon="bosch-ic-settings-editor"></mat-icon>
                </button>
                <button mat-icon-button class="action-btn delete" title="Delete Collection"
                  [disabled]="!canCRUDKnowledge(collection)"
                  (click)="deleteCollection(collection); $event.stopPropagation()">
                  <mat-icon fontIcon="Bosch-Ic-delete"></mat-icon>
                </button>
                <button mat-icon-button class="action-btn expand" title="Expand/Collapse"
                  (click)="expend(collection); $event.stopPropagation()">
                  <mat-icon [fontIcon]="collection.isExpend ? 'bosch-ic-up' : 'bosch-ic-down'"></mat-icon>
                </button>
              </div>
            </div>
          </mat-card>
          <div *ngIf="collection.isExpend">
            <div class="mat-table-container">
              <mat-table [dataSource]="collection.pages || []">
              <ng-container matColumnDef="title">
                <mat-header-cell *matHeaderCellDef>Page Title</mat-header-cell>
                <mat-cell *matCellDef="let page">
                  <a [href]="page.url || '#'" target="_blank" rel="noopener noreferrer" [title]="page.title || 'N/A'">
                    {{ page.title || 'N/A' }}
                  </a>
                </mat-cell>
              </ng-container>
              <ng-container matColumnDef="isIncludeChild">
                <mat-header-cell *matHeaderCellDef>Childs?</mat-header-cell>
                <mat-cell *matCellDef="let page">{{ page.isIncludeChild ? 'Yes' : 'No' }}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="isEmbedding">
                <mat-header-cell *matHeaderCellDef>Embeded?</mat-header-cell>
                <mat-cell *matCellDef="let page">{{ page.isEmbedding ? 'Yes' : 'No' }}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="contentNumber">
                <mat-header-cell *matHeaderCellDef>Contents</mat-header-cell>
                <mat-cell *matCellDef="let page">{{ page.contentNumber || 0 }}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="versionNo">
                <mat-header-cell *matHeaderCellDef>Ver.</mat-header-cell>
                <mat-cell *matCellDef="let page">{{ page.versionNo || 'N/A' }}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="embeddingVersionNo">
                <mat-header-cell *matHeaderCellDef>Embeded Ver.</mat-header-cell>
                <mat-cell *matCellDef="let page">{{ page.embeddingVersionNo || 'N/A' }}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="modificationTime">
                <mat-header-cell *matHeaderCellDef>Last Modified</mat-header-cell>
                <mat-cell *matCellDef="let page">{{ (page.modificationTime | date:'yyyy-MM-dd HH:mm:ss') || 'N/A' }}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="actions">
                <mat-header-cell *matHeaderCellDef class="actions-header"></mat-header-cell>
                <mat-cell *matCellDef="let page" class="actions-cell">
                  <div class="subpage-actions">
                    <button mat-icon-button title="Update & Embed Page Contents"
                      (click)="updateAndEmbedPageContents(page, collection); $event.stopPropagation()">
                      <mat-icon fontIcon="bosch-ic-refresh-cloud"></mat-icon>
                    </button>
                    <button mat-icon-button title="Delete Page"
                      [disabled]="!canCRUDKnowledge(collection)"
                      (click)="deletePage(collection, page); $event.stopPropagation()">
                      <mat-icon fontIcon="Bosch-Ic-delete"></mat-icon>
                    </button>
                  </div>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let page; columns: displayedColumns"></mat-row>
            </mat-table>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页控件 -->
      <div class="pagination-container" *ngIf="itemCount > 0">
        <bci-paginator [showPageSizeSelector]="true" [length]="itemCount || 0" [pageIndex]="pageNumber || 0"
          [pageSize]="pageSize || 10" [pageSizeOptions]="pageSizeOptions" (page)="onPageChange($any($event).detail)">
        </bci-paginator>
      </div>

      <!-- 空状态 -->
      <bci-empty-state class="center-in-space" *ngIf="itemCount === 0">
        No Collections
      </bci-empty-state>
    </div>
  </bci-master-view>
</bci-page-content>
