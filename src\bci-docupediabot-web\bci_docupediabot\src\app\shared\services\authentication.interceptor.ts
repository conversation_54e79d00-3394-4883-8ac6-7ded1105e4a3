import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, filter, take, switchMap } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';

@Injectable()
export class AuthenticationInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor(private authService: AuthService) {}

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {

    const authReq = this.addTokenHeader(req, this.authService.getToken());

    return next.handle(authReq).pipe(
      catchError((error) => {
        if (error instanceof HttpErrorResponse && error.status === 401) {
          return this.handle401Error(authReq, next);
        }
        return throwError(() => error);
      })
    );
  }

  private addTokenHeader(request: HttpRequest<any>, token: string | null): HttpRequest<any> {
    if (token) {
      return request.clone({
        headers: request.headers.set('Authorization', `Bearer ${token}`)
      });
    }
    return request;
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

    if (request.url.includes('/auth/refresh')) {
      console.warn('Refresh token request failed - redirecting to login');
      this.authService.logout();
      return throwError(() => new Error('Refresh token expired - Please login again'));
    }

    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      const refreshToken = this.authService.getRefreshToken();
      if (refreshToken) {
        console.log('Attempting to refresh token...');
        return this.authService.refreshToken().pipe(
          switchMap((success: boolean) => {
            this.isRefreshing = false;
            if (success) {
              console.log('Token refreshed successfully, retrying original request');
              this.refreshTokenSubject.next(this.authService.getToken());
              return next.handle(this.addTokenHeader(request, this.authService.getToken()));
            } else {
              console.warn('Token refresh failed - redirecting to login');
              this.authService.logout();
              return throwError(() => new Error('Token refresh failed'));
            }
          }),
          catchError((err) => {
            console.error('Token refresh error:', err);
            this.isRefreshing = false;
            this.authService.logout();
            return throwError(() => err);
          })
        );
      } else {
        console.warn('No refresh token available - redirecting to login');
        this.isRefreshing = false;
        this.authService.logout();
        return throwError(() => new Error('No refresh token available'));
      }
    } else {

      return this.refreshTokenSubject.pipe(
        filter(token => token !== null),
        take(1),
        switchMap(() => next.handle(this.addTokenHeader(request, this.authService.getToken())))
      );
    }
  }
}
