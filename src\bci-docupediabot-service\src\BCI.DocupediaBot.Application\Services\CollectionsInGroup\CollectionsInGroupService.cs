using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.CollectionsInGroup
{
  public class CollectionsInGroupService : ICollectionsInGroupService
  {
    private readonly ICollectionsInGroupRepository _collectionsInGroupRepository;
    private readonly ICurrentUserAccessor _currentUserAccessor;
    private readonly ILogger<CollectionsInGroupService> _logger;

    public CollectionsInGroupService(
      ICollectionsInGroupRepository collectionsInGroupRepository,
      ICurrentUserAccessor currentUserAccessor,
      ILogger<CollectionsInGroupService> logger)
    {
      _collectionsInGroupRepository = collectionsInGroupRepository;
      _currentUserAccessor = currentUserAccessor;
      _logger = logger;
    }

    public async Task<List<Guid>> GetGroupIdsByCollectionIdAsync(Guid collectionId)
    {
      _logger.LogInformation("Getting group IDs for collection {CollectionId}", collectionId);
      return await _collectionsInGroupRepository.GetGroupIdsByCollectionIdAsync(collectionId);
    }

    public async Task<List<Guid>> GetCollectionIdsByGroupIdAsync(Guid groupId)
    {
      _logger.LogInformation("Getting collection IDs for group {GroupId}", groupId);
      return await _collectionsInGroupRepository.GetCollectionIdsByGroupIdAsync(groupId);
    }

    public async Task<List<Guid>> GetCollectionIdsByGroupIdsAsync(IEnumerable<Guid> groupIds)
    {
      var groupIdList = groupIds.ToList();
      _logger.LogInformation("Getting collection IDs for {Count} groups", groupIdList.Count);
      return await _collectionsInGroupRepository.GetCollectionIdsByGroupIdsAsync(groupIdList);
    }

    public async Task<bool> ExistsMappingAsync(Guid collectionId, Guid groupId)
    {
      _logger.LogInformation("Checking if mapping exists between collection {CollectionId} and group {GroupId}", collectionId, groupId);
      return await _collectionsInGroupRepository.ExistsMappingAsync(collectionId, groupId);
    }

    public async Task AddMappingAsync(Guid collectionId, Guid groupId)
    {
      _logger.LogInformation("Adding mapping between collection {CollectionId} and group {GroupId}", collectionId, groupId);

      var exists = await _collectionsInGroupRepository.ExistsMappingAsync(collectionId, groupId);
      if (exists)
      {
        _logger.LogWarning("Mapping already exists between collection {CollectionId} and group {GroupId}", collectionId, groupId);
        return;
      }

      var mapping = new Domain.Entities.CollectionsInGroup
      {
        CollectionId = collectionId,
        GroupId = groupId
      };

      await _collectionsInGroupRepository.CreateAsync(mapping);
      _logger.LogInformation("Successfully added mapping between collection {CollectionId} and group {GroupId}", collectionId, groupId);
    }

    public async Task AddMappingsAsync(Guid collectionId, IEnumerable<Guid> groupIds)
    {
      var groupIdList = groupIds.ToList();
      _logger.LogInformation("Adding {Count} mappings for collection {CollectionId}", groupIdList.Count, collectionId);

      foreach (var groupId in groupIdList)
      {
        await AddMappingAsync(collectionId, groupId);
      }
    }

    public async Task DeleteByCollectionIdAsync(Guid collectionId)
    {
      _logger.LogInformation("Deleting all mappings for collection {CollectionId}", collectionId);
      await _collectionsInGroupRepository.DeleteByCollectionIdAsync(collectionId);
    }

    public async Task DeleteByGroupIdAsync(Guid groupId)
    {
      _logger.LogInformation("Deleting all mappings for group {GroupId}", groupId);
      await _collectionsInGroupRepository.DeleteByGroupIdAsync(groupId);
    }

    public async Task DeleteMappingAsync(Guid collectionId, Guid groupId)
    {
      _logger.LogInformation("Deleting mapping between collection {CollectionId} and group {GroupId}", collectionId, groupId);
      await _collectionsInGroupRepository.DeleteMappingAsync(collectionId, groupId);
    }

    public async Task UpdateCollectionGroupsAsync(Guid collectionId, IEnumerable<Guid> groupIds)
    {
      var groupIdList = groupIds.ToList();
      _logger.LogInformation("Updating groups for collection {CollectionId} with {Count} groups", collectionId, groupIdList.Count);


      await DeleteByCollectionIdAsync(collectionId);


      if (groupIdList.Any())
      {
        await AddMappingsAsync(collectionId, groupIdList);
      }

      _logger.LogInformation("Successfully updated groups for collection {CollectionId}", collectionId);
    }

    public async Task<Dictionary<Guid, List<Guid>>> GetGroupIdsByCollectionIdsBatchAsync(IEnumerable<Guid> collectionIds)
    {
      var collectionIdList = collectionIds.ToList();
      _logger.LogInformation("Getting group IDs for {Count} collections in batch", collectionIdList.Count);
      return await _collectionsInGroupRepository.GetGroupIdsByCollectionIdsBatchAsync(collectionIdList);
    }
  }
}
