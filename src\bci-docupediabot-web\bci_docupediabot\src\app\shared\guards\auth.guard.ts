import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '@shared/services/auth.service';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    return this.checkAuth(state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    return this.checkAuth(state.url);
  }

  private checkAuth(url: string): Observable<boolean> | boolean {

    if (this.authService.isLoggedIn()) {
      return true;
    }


    const refreshToken = this.authService.getRefreshToken();
    if (refreshToken) {
      console.log('Token expired, attempting automatic refresh...');
      return this.authService.refreshToken().pipe(
        map((success: boolean) => {
          if (success) {
            console.log('Token refresh successful - user can continue');
            return true;
          } else {
            console.warn('Token refresh failed - session expired, redirecting to login');

            this.router.navigate(['']);
            return false;
          }
        }),
        catchError((error) => {
          console.error('Token refresh error - network or server issue:', error);
          this.router.navigate(['']);
          return of(false);
        })
      );
    }


    console.log('No valid token or refresh token, redirecting to login');
    this.router.navigate(['']);
    return false;
  }
}
