import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { LoginSuccessModel, SysUserResponseDTO } from '@shared/models/system.model';
import { BehaviorSubject, catchError, map, Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly TOKEN_KEY = 'token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly EXPIRY_KEY = 'auth_expiry';
  private readonly USER_KEY = 'user_info';

  private readonly TOKEN_VALIDITY_MINUTES = 110;

  private readonly REFRESH_THRESHOLD_MINUTES = 5;

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(
    this.hasValidToken()
  );
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  private refreshTimer: any;

  constructor(private router: Router, private http: HttpClient) {

    setTimeout(() => {
      this.checkTokenValidity();

      if (this.hasValidToken()) {
        this.startTokenRefreshTimer();
      }
    }, 0);
  }

  private apiUrl = `${environment.baseUrl}/api/auth`;

  login(userid: string, password: string): Observable<boolean> {
    const payload = { userid, password };
    return this.http
      .post<LoginSuccessModel>(`${this.apiUrl}/login`, payload)
      .pipe(
        map((response) => {

          localStorage.setItem(this.TOKEN_KEY, response.token);
          localStorage.setItem(this.REFRESH_TOKEN_KEY, response.refreshToken);


          const customExpiryDate = new Date();
          customExpiryDate.setMinutes(customExpiryDate.getMinutes() + this.TOKEN_VALIDITY_MINUTES);
          localStorage.setItem(this.EXPIRY_KEY, customExpiryDate.toISOString());


          localStorage.setItem(this.USER_KEY, JSON.stringify(response.sysUserResponseDTO));

          this.isAuthenticatedSubject.next(true);
          this.startTokenRefreshTimer();
          return true;
        })
      );
  }

  logout(): void {
    console.log('Logging out user...');
    this.clearTokenRefreshTimer();
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.EXPIRY_KEY);
    localStorage.removeItem(this.USER_KEY);
    this.isAuthenticatedSubject.next(false);


    setTimeout(() => {
      this.router.navigate(['/']);
    }, 0);
  }

  isLoggedIn(): boolean {
    return this.hasValidToken();
  }

  getToken(): string | null {
    if (this.hasValidToken()) {
      return localStorage.getItem(this.TOKEN_KEY);
    }
    return null;
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  getCurrentUser(): SysUserResponseDTO | null {
    const userJson = localStorage.getItem(this.USER_KEY);
    return userJson ? JSON.parse(userJson) as SysUserResponseDTO : null;
  }

  public updateCurrentUser(updatedUser: SysUserResponseDTO): void {
    console.log("updatedUser");
    console.log(updatedUser);
    localStorage.setItem(this.USER_KEY, JSON.stringify(updatedUser));
  }

  private hasValidToken(): boolean {
    const token = localStorage.getItem(this.TOKEN_KEY);
    const expiry = localStorage.getItem(this.EXPIRY_KEY);

    if (!token || !expiry) {
      return false;
    }

    const expiryDate = new Date(expiry);
    const now = new Date();

    if (now > expiryDate) {
      console.log('Token expired, attempting refresh...');

      return false;
    }

    return true;
  }

  private checkTokenValidity(): void {
    const isValid = this.hasValidToken();
    this.isAuthenticatedSubject.next(isValid);
  }

  refreshToken(): Observable<boolean> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      return of(false);
    }

    const payload = { refreshToken };
    return this.http
      .post<{ token: string; refreshToken: string }>(`${this.apiUrl}/refresh`, payload)
      .pipe(
        map((response) => {

          localStorage.setItem(this.TOKEN_KEY, response.token);
          localStorage.setItem(this.REFRESH_TOKEN_KEY, response.refreshToken);


          const customExpiryDate = new Date();
          customExpiryDate.setMinutes(customExpiryDate.getMinutes() + this.TOKEN_VALIDITY_MINUTES);
          localStorage.setItem(this.EXPIRY_KEY, customExpiryDate.toISOString());

          this.isAuthenticatedSubject.next(true);
          this.startTokenRefreshTimer();
          return true;
        }),
        catchError((error) => {
          console.error('Token refresh failed:', error);
          this.logout();
          return of(false);
        })
      );
  }

  getTokenRemainingHours(): number {
    const expiry = localStorage.getItem(this.EXPIRY_KEY);
    if (!expiry) return 0;

    const expiryDate = new Date(expiry);
    const now = new Date();
    const diffMs = expiryDate.getTime() - now.getTime();

    return Math.max(0, Math.ceil(diffMs / (1000 * 60 * 60)));
  }

  getTokenRemainingDays(): number {
    const expiry = localStorage.getItem(this.EXPIRY_KEY);
    if (!expiry) return 0;

    const expiryDate = new Date(expiry);
    const now = new Date();
    const diffMs = expiryDate.getTime() - now.getTime();

    return Math.max(0, Math.ceil(diffMs / (1000 * 60 * 60 * 24)));
  }

  private startTokenRefreshTimer(): void {
    this.clearTokenRefreshTimer();

    const expiry = localStorage.getItem(this.EXPIRY_KEY);
    if (!expiry) return;

    const expiryDate = new Date(expiry);
    const now = new Date();
    const refreshTime = new Date(expiryDate.getTime() - this.REFRESH_THRESHOLD_MINUTES * 60 * 1000);

    const timeUntilRefresh = refreshTime.getTime() - now.getTime();

    if (timeUntilRefresh > 0) {
      console.log(`Token refresh scheduled in ${Math.round(timeUntilRefresh / 1000 / 60)} minutes`);
      this.refreshTimer = setTimeout(() => {
        this.attemptTokenRefresh();
      }, timeUntilRefresh);
    } else {

      this.attemptTokenRefresh();
    }
  }

  private clearTokenRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  private attemptTokenRefresh(): void {
    const refreshToken = this.getRefreshToken();
    if (refreshToken) {
      console.log('Automatically refreshing token...');
      this.refreshToken().subscribe({
        next: (success) => {
          if (success) {
            console.log('Token auto-refresh successful');
          } else {
            console.warn('Token auto-refresh failed');
          }
        },
        error: (error) => {
          console.error('Token auto-refresh error:', error);
        }
      });
    }
  }
}
