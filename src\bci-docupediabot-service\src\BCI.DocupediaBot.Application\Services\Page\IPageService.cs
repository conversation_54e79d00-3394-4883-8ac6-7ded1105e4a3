﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace BCI.DocupediaBot.Application.Services.Page
{
  public interface IPageService
	{
		Task<List<PageResponseDTO>> QueryPagesByCollectionIdAsync(Guid collectionId);
		Task<ResponseResult> DeletePageByIdAsync(Guid pageId, Guid collectionId);
		Task<ResponseResult> UpdatePageByIdAsync(Guid pageId);
		Task<PageResponseDTO> AddPageAsync(PageAddDTO dto);
		Task<PageResponseDTO> QueryPageById(Guid pageId);
		Task<ResponseResult> UpdateContentAsync(PageResponseDTO result, Guid collectionId);
		Task<ResponseResult> EmbedContentAsync(PageResponseDTO result, Guid collectionId, EmbeddingModel embeddingModel);
		Task<ResponseResult> UpdateContentAndEmbedAsync(PageResponseDTO result, Guid collectionId, EmbeddingModel embeddingModel);
    Task<string> GetContentSummariesAsync(Guid collectionId);
    Task<string> GetRecentDocumentsAsync(Guid collectionId);
  }
}
