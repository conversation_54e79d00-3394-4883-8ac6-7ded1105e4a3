﻿using AutoMapper;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Page
{
  public class ContentsInPageService : IContentsInPageService
	{
		private readonly IContentsInPageRepository _repository;
		private readonly IMapper _mapper;

		public ContentsInPageService(IContentsInPageRepository repository, IMapper mapper)
		{
			_repository = repository;
			_mapper = mapper;
		}

		public async Task<ResponseResult> DeleteMappingAsync(Guid contentId, Guid pageId)
		{
			var relation = await _repository.QueryAsync(r => r.ContentId == contentId && r.PageId == pageId);

			if (relation == null || !relation.Any())
				return new ResponseResult { IsSuccess = true, Msg = $"No content relationship found for content {contentId}." };

			await _repository.DeleteAsync(relation[0]);
			return new ResponseResult
			{
				IsSuccess = true,
				Msg = $"Deleted content relationship for content {contentId}."
			};
		}

		public async Task<List<Guid>> QueryContentIdsByPageIdAsync(Guid pageId)
		{
			var allRelations = await _repository.GetAllAsync();
			return allRelations
					.Where(r => r.PageId == pageId)
					.Select(r => r.ContentId)
					.ToList();
		}

		public async Task AddMappingAsync(Guid pageId, Guid contentId)
		{
			// Check if mapping already exists to avoid duplicate key constraint violation
			var allRelations = await _repository.GetAllAsync();
			var existingMapping = allRelations.FirstOrDefault(r => r.PageId == pageId && r.ContentId == contentId);

			if (existingMapping != null)
			{
				// Mapping already exists, no need to create a new one
				return;
			}

			var mapping = new ContentsInPage
			{
				PageId = pageId,
				ContentId = contentId
			};
			await _repository.CreateAsync(mapping);
		}

		public async Task<List<ContentsInPage>> QueryMappingsAsync()
		{
			return await _repository.GetAllAsync();
		}

		public async Task<ResponseResult> DeleteMappingsByContentIdAsync(Guid contentId)
		{
			var allRelations = await _repository.GetAllAsync();
			var relationsToDelete = allRelations.Where(r => r.ContentId == contentId).ToList();

			if (!relationsToDelete.Any())
				return new ResponseResult { IsSuccess = true, Msg = $"No mappings found to delete for Content {contentId}." };

			await _repository.DeleteRangeAsync(relationsToDelete);
			return new ResponseResult
			{
				IsSuccess = true,
				Msg = $"Deleted {relationsToDelete.Count} mappings for Content {contentId}."
			};
		}
	}
}