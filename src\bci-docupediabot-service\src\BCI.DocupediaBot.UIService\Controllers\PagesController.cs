﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Embedding;
using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Application.Services.CollectPageContent;
using BCI.DocupediaBot.Application.Services.Page;
using BCI.DocupediaBot.Application.Services.SysUser;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using BCI.DocupediaBot.Infrastructure.Constants;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.UIService.Controllers
{
  [Route("api/pages")]
	[ApiController]
  [Authorize]
  public class PagesController : ControllerBase
	{
		private readonly IPageService _pageService;
		private readonly ISysUserService _sysUserService;
		private readonly ICurrentUserAccessor _currentUserAccessor;
		private readonly ILogger<PagesController> _logger;

		public PagesController(
				IPageService pageService,
				ISysUserService sysUserService,
				ICurrentUserAccessor currentUserAccessor,
				ILogger<PagesController> logger)
		{
			_pageService = pageService ?? throw new ArgumentNullException(nameof(pageService));
			_sysUserService = sysUserService ?? throw new ArgumentNullException(nameof(sysUserService));
			_currentUserAccessor = currentUserAccessor ?? throw new ArgumentNullException(nameof(currentUserAccessor));
			_logger = logger ?? throw new ArgumentNullException(nameof(logger));
		}

		[HttpPost]
		public async Task<IActionResult> CreateAsync([FromBody] PageAddDTO dto)
		{
			if (dto == null)
			{
				_logger.LogWarning("Invalid page creation request: {Message}", ErrorMessages.PageDataEmpty);
				return BadRequest(ApiResponse<PageResponseDTO>.Error(ErrorMessages.PageDataEmpty));
			}

			_logger.LogInformation("Creating page from URL: {Url}", dto.Url);

			try
			{

				if (string.IsNullOrEmpty(dto.SourceId) && !string.IsNullOrEmpty(dto.Url))
				{
					var currentUserId = _currentUserAccessor.UserId;
					if (string.IsNullOrEmpty(currentUserId))
					{
						_logger.LogWarning("Unable to get current user ID for URL resolution");
						return BadRequest(ApiResponse<PageResponseDTO>.Error("Unable to identify current user"));
					}

					var currentUser = await _sysUserService.QueryUserByNTAccountAsync(currentUserId);
					if (currentUser == null || string.IsNullOrEmpty(currentUser.DocupediaToken))
					{
						_logger.LogWarning("User {UserId} does not have a valid Docupedia token", currentUserId);
						return BadRequest(ApiResponse<PageResponseDTO>.Error("Please configure your Docupedia token in your profile"));
					}

					dto.UserToken = currentUser.DocupediaToken;
				}

				_logger.LogInformation("Creating page with data: {@Dto}", dto);
				PageResponseDTO result = await _pageService.AddPageAsync(dto);
				return Ok(ApiResponse<PageResponseDTO>.Ok(result));
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to create page: {@Dto}", dto);
				return StatusCode(500, ApiResponse<PageResponseDTO>.Error(ErrorMessages.InternalServerError));
			}
		}

		[HttpDelete("{pageId}/{collectionId}")]
		public async Task<IActionResult> DeleteAsync(Guid pageId, Guid collectionId)
		{
			if (pageId == Guid.Empty || collectionId == Guid.Empty)
			{
				_logger.LogWarning("Invalid page deletion request: {Message}", ErrorMessages.PageOrCollectionIdEmpty);
				return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.PageOrCollectionIdEmpty));
			}

			try
			{
				_logger.LogInformation("Deleting page: {PageId} from collection: {CollectionId}", pageId, collectionId);
				ResponseResult result = await _pageService.DeletePageByIdAsync(pageId, collectionId);
				return Ok(ApiResponse<ResponseResult>.Ok(result));
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to delete page: {PageId}", pageId);
				return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
			}
		}

		[HttpGet("collection/{collectionId}")]
		public async Task<IActionResult> GetByCollectionAsync(Guid collectionId)
		{
			if (collectionId == Guid.Empty)
			{
				_logger.LogWarning("Invalid page query request: {Message}", ErrorMessages.CollectionIdEmpty);
				return BadRequest(ApiResponse<string>.Error(ErrorMessages.CollectionIdEmpty));
			}

			try
			{
				_logger.LogInformation("Querying pages for collection: {CollectionId}", collectionId);
				var result = await _pageService.QueryPagesByCollectionIdAsync(collectionId);
				return Ok(ApiResponse<List<PageResponseDTO>>.Ok(result));
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to query pages for collection: {CollectionId}", collectionId);
				return StatusCode(500, ApiResponse<string>.Error(ErrorMessages.InternalServerError));
			}
		}

		[HttpGet("{pageId}")]
		public async Task<IActionResult> GetByIdAsync(Guid pageId)
		{
			if (pageId == Guid.Empty)
			{
				_logger.LogWarning("Invalid page query request: {Message}", ErrorMessages.PageIdEmpty);
				return BadRequest(ApiResponse<Page>.Error(ErrorMessages.PageIdEmpty));
			}

			try
			{
				_logger.LogInformation("Querying page: {PageId}", pageId);
				var result = await _pageService.QueryPageById(pageId);
				return Ok(ApiResponse<PageResponseDTO>.Ok(result));
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to query page: {PageId}", pageId);
				return StatusCode(500, ApiResponse<Page>.Error(ErrorMessages.InternalServerError));
			}
		}

		[HttpPut("{pageId}")]
		public async Task<IActionResult> UpdateAsync(Guid pageId)
		{
			if (pageId == Guid.Empty)
			{
				_logger.LogWarning("Invalid page update request: {Message}", ErrorMessages.PageIdEmpty);
				return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.PageIdEmpty));
			}

			try
			{
				_logger.LogInformation("Updating page: {PageId}", pageId);
				ResponseResult result = await _pageService.UpdatePageByIdAsync(pageId);
				return Ok(ApiResponse<ResponseResult>.Ok(result));
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to update page: {PageId}", pageId);
				return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
			}
		}

    [HttpPost("{pageId}/contents/update/{collectionId}")]
    public async Task<IActionResult> UpdateContentsAsync(Guid pageId, Guid collectionId)
    {
      if (pageId == Guid.Empty || collectionId == Guid.Empty)
      {
        _logger.LogWarning("Invalid content update request: {Message}", ErrorMessages.PageOrCollectionIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.PageOrCollectionIdEmpty));
      }

      try
      {
        _logger.LogInformation("Updating contents for page: {PageId} in collection: {CollectionId}", pageId, collectionId);
        var page = await _pageService.QueryPageById(pageId);
        var result = await _pageService.UpdateContentAsync(page, collectionId);
        return Ok(ApiResponse<ResponseResult>.Ok(new ResponseResult { IsSuccess = true, Msg = "Contents updated successfully" }));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to update contents for page: {PageId}", pageId);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }

    [HttpPost("{pageId}/contents/embed/{collectionId}")]
    public async Task<IActionResult> EmbedContentsAsync(Guid pageId, Guid collectionId, [FromBody] EmbeddingModelRequestDTO request)
    {
      if (pageId == Guid.Empty || collectionId == Guid.Empty)
      {
        _logger.LogWarning("Invalid content embed request: {Message}", ErrorMessages.PageOrCollectionIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.PageOrCollectionIdEmpty));
      }

      try
      {
        _logger.LogInformation("Embedding contents for page: {PageId} in collection: {CollectionId} with embedding model: {EmbeddingModel}", pageId, collectionId, request.EmbeddingModel);
        var page = await _pageService.QueryPageById(pageId);
        var result = await _pageService.EmbedContentAsync(page, collectionId, request.EmbeddingModel);
        return Ok(ApiResponse<ResponseResult>.Ok(new ResponseResult { IsSuccess = true, Msg = "Contents embedded successfully" }));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to embed contents for page: {PageId}", pageId);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }

    [HttpPost("{pageId}/contents/update-and-embed/{collectionId}")]
    public async Task<IActionResult> UpdateAndEmbedContentsAsync(Guid pageId, Guid collectionId, [FromBody] EmbeddingModelRequestDTO request)
    {
      if (pageId == Guid.Empty || collectionId == Guid.Empty)
      {
        _logger.LogWarning("Invalid content update and embed request: {Message}", ErrorMessages.PageOrCollectionIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.PageOrCollectionIdEmpty));
      }

      try
      {
        _logger.LogInformation("Updating and embedding contents for page: {PageId} in collection: {CollectionId} with embedding model: {EmbeddingModel}", pageId, collectionId, request.EmbeddingModel);
        var page = await _pageService.QueryPageById(pageId);
        var result = await _pageService.UpdateContentAndEmbedAsync(page, collectionId, request.EmbeddingModel);
        return Ok(ApiResponse<ResponseResult>.Ok(new ResponseResult { IsSuccess = true, Msg = "Contents updated and embedded successfully" }));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to update and embed contents for page: {PageId}", pageId);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }
  }
}