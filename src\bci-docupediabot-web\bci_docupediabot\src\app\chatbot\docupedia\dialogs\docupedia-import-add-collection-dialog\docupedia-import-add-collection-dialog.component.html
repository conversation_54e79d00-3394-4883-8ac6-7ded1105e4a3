<bci-modal-component
  [title]="data.mode === 'add' ? 'Add Collection' : 'Edit Collection'"
  [closeIcon]="true"
  (closeHandler)="close()">
  <p class="modal-subheading">Enter collection information below.</p>
  <form [formGroup]="collectionForm" class="modal-form">
    <mat-form-field appearance="fill" class="full-width">
      <mat-label>Collection Name</mat-label>
      <input matInput formControlName="name" required placeholder="Collection Name" />
      <mat-error *ngIf="collectionForm.get('name')?.hasError('required')">
        Collection name is required
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="fill" class="full-width">
      <mat-label>Comment</mat-label>
      <input matInput formControlName="comment" placeholder="Comment" />
    </mat-form-field>

    <mat-form-field appearance="fill" class="full-width">
      <mat-label>Groups</mat-label>
      <mat-select formControlName="groupIds" multiple>
        <mat-option *ngFor="let group of availableGroups" [value]="group.id">
          {{ group.name }}
        </mat-option>
      </mat-select>
      <mat-hint>Select groups that can access this collection</mat-hint>
    </mat-form-field>

    <mat-form-field appearance="fill" class="full-width">
      <mat-label>Embedding Model (Cannot Edit)</mat-label>
      <mat-select formControlName="embeddingModel" [disabled]="data.mode === 'edit'">
        <mat-option *ngFor="let option of embeddingModels"
                    [value]="option.value"
                    [matTooltip]="option.tooltip">
          {{ option.label }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-slide-toggle formControlName="isAutomaticUpdate" class="full-width">
      Automatic Update
    </mat-slide-toggle>
    <br />
    <div *ngIf="collectionForm.get('isAutomaticUpdate')?.value" class="auto-update-section">
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Update Frequency</mat-label>
        <mat-select formControlName="updateType">
          <mat-option *ngFor="let option of updateTypes" [value]="option.value">
            {{ option.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field
        *ngIf="shouldShowInterval()"
        appearance="fill"
        class="full-width">
        <mat-label>Interval (0 ~ 30)</mat-label>
        <input matInput #number type="number" placeholder="Enter a value"
        formControlName="intervalNumber" [required]="shouldShowInterval()"
        aria-label="value modificator" value="1" max="30" min="1" />
        <span matSuffix bciSpanForValueModificatorButton>
          <button type="button" (click)="stepDown(number, $event)">
            <mat-icon fontIcon="Bosch-Ic-less-minimize" aria-label="minus"></mat-icon>
          </button>
          <button type="button" (click)="stepUp(number, $event)">
            <mat-icon fontIcon="Bosch-Ic-add" aria-label="add"></mat-icon>
          </button>
        </span>
        <mat-error *ngIf="collectionForm.get('intervalNumber')?.hasError('required')">
          Interval number is required
        </mat-error>
        <mat-error *ngIf="collectionForm.get('intervalNumber')?.hasError('min')">
          Interval must be at least 1
        </mat-error>
      </mat-form-field>

      <bci-datetime-picker
        class="half-width"
        label="Update Time"
        [default]="collectionForm.get('updateTime')?.value"
        range-date-time="false"
        min-date="1577829600000"
        max-date="1982927200000"
        time-format="HH:mm:ss"
        hide="calendar"
        (newDateSelected)="listenDateChanged($event)"
      ></bci-datetime-picker>
    </div>
  </form>

  <div actions style="margin: 0 20px 0 0;">
    <button bciPrimaryButton
            [disabled]="submitDisabled"
            (click)="save()">
      {{ data.mode === 'add' ? 'Add' : 'Save' }}
    </button>
    <button bciSecondaryButton
            (click)="close()">
      Cancel
    </button>
  </div>
</bci-modal-component>
